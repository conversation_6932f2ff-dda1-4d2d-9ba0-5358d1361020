<template>
  <div class="power-monitoring-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          三维智能联动平台实现全球精准决策与毫秒级响应。
        </h1>
        <p class="hero-subtitle">
          构建变电站级三维孪生体,融合SCADA实时数据与设备
          历史档案,实现从主变压器到绝缘子的全生命周期健康管
          理,支持设备拆解透视与故障回溯
        </p>
        <button class="hero-button">See more</button>
      </div>
    </section>

    <!-- 3D Isometric Section -->
    <section class="isometric-section">
      <!-- Top Badge -->
      <div class="top-badge">一体化解决方案</div>

      <h2 class="section-title">覆盖配电的设计、建造、运营、维护的全场景应用</h2>
      <p class="section-subtitle">通过配电运营管理、可视化运维管理一体化设计，持续提升专业化能力，<br>为构建新型电力系统提供有力支撑和专业保障。</p>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn primary">
          <span class="btn-icon">🎯</span>
          智能分析
        </button>
        <button class="action-btn secondary">
          <span class="btn-icon">💡</span>
          精准决策
        </button>
      </div>

      <div class="isometric-container">
        <div class="isometric-model">
          <!-- 3D Platform Image Placeholder -->
          <div class="platform-image-container">
            <img src="/api/placeholder/400/300" alt="3D电力监控平台" class="platform-image">
            <!-- 如果需要使用本地图片，请替换上面的src为实际图片路径 -->
          </div>

          <!-- Connection Lines -->
          <svg class="connection-lines" viewBox="0 0 800 600">
            <!-- Line to card-1 -->
            <path d="M200 200 Q150 150 100 120" stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
            </path>
            <!-- Line to card-2 -->
            <path d="M600 200 Q650 150 700 120" stroke="#3498db" stroke-width="2" fill="none" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
            </path>
            <!-- Line to card-3 -->
            <path d="M200 400 Q150 450 100 480" stroke="#27ae60" stroke-width="2" fill="none" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
            </path>
            <!-- Line to card-4 -->
            <path d="M600 400 Q650 450 700 480" stroke="#f39c12" stroke-width="2" fill="none" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
            </path>
          </svg>

          <!-- Floating Data Cards -->
          <div class="data-card card-1">
            <div class="card-icon red">📊</div>
            <div class="card-header">连接收集</div>
            <div class="card-content">
              <div class="data-item">网络数字化运营</div>
              <div class="data-item">智能化改造</div>
            </div>
          </div>

          <div class="data-card card-2">
            <div class="card-icon blue">🎯</div>
            <div class="card-header">治理优化</div>
            <div class="card-content">
              <div class="data-item">数字智能管理</div>
              <div class="data-item">二次设备监测</div>
              <div class="data-item">AI优化分析</div>
            </div>
          </div>

          <div class="data-card card-3">
            <div class="card-icon green">📈</div>
            <div class="card-header">监测分析</div>
            <div class="card-content">
              <div class="data-item">电气系统监控</div>
              <div class="data-item">电能质量分析</div>
              <div class="data-item">能效效率分析</div>
            </div>
          </div>

          <div class="data-card card-4">
            <div class="card-icon orange">⚡</div>
            <div class="card-header">治理优化</div>
            <div class="card-content">
              <div class="data-item">数字智能管理</div>
              <div class="data-item">二次设备监测</div>
              <div class="data-item">AI优化分析</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Text Section -->
    <section class="text-section">
      <div class="text-content">
        <p class="main-text">在国际局势复杂多变与低碳发展双重驱动下,依托自主可控技术保障电力系统稳定高效运行</p>
      </div>
    </section>

    <!-- Monitoring Interface Section -->
    <section class="monitoring-section">
      <div class="wave-background"></div>
      <div class="monitoring-content">
        <h2 class="section-title">全面打通连接、监测、治理的全数字化<br>流程,形成智能配电闭环价值落地</h2>
        <div class="monitoring-display">
          <div class="laptop-frame">
            <div class="laptop-screen">
              <div class="monitoring-interface">
                <div class="interface-header">电力监控系统</div>
                <div class="interface-content">
                  <div class="control-room-view">
                    <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=800&h=400&fit=crop" alt="Control Room" class="control-room-image">
                  </div>
                  <div class="monitoring-panels">
                    <div class="panel-item">
                      <span class="panel-label">主变压器</span>
                      <span class="panel-status active"></span>
                    </div>
                    <div class="panel-item">
                      <span class="panel-label">配电柜</span>
                      <span class="panel-status active"></span>
                    </div>
                    <div class="panel-item">
                      <span class="panel-label">监控系统</span>
                      <span class="panel-status warning"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="laptop-base"></div>
          </div>
        </div>
        <div class="monitoring-features">
          <div class="feature-item">
            <h4>实时监控</h4>
            <p>24小时不间断监控电力系统运行状态</p>
          </div>
          <div class="feature-item">
            <h4>智能分析</h4>
            <p>AI算法分析设备运行数据，预测潜在故障</p>
          </div>
          <div class="feature-item">
            <h4>远程控制</h4>
            <p>支持远程操作和控制，提高运维效率</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios Section -->
    <section class="app-scenarios-section">
      <h2 class="section-title-dark">电力监控系统应用场景</h2>
      <p class="section-subtitle-dark">三大类电力用户,N种安定设备,N种解决方案</p>
      <div class="scenarios-grid">
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400&h=250&fit=crop" alt="老旧电网" class="scenario-card-image">
          <div class="card-content">
            <h3>老旧电网</h3>
            <p>设备智能化改造，提高效率50%</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=250&fit=crop" alt="城市变电站" class="scenario-card-image">
          <div class="card-content">
            <h3>城市变电站</h3>
            <p>三维孪生可视化，辅助决策</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=250&fit=crop" alt="新建电厂" class="scenario-card-image">
          <div class="card-content">
            <h3>新建电厂</h3>
            <p>数字化生产平台，运维成本降低30%</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Page logic for Power Monitoring Solution
</script>

<style scoped>
.power-monitoring-page {
  background-color: #000;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.section-title {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-title-dark {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-subtitle-dark {
  font-size: 1.1rem;
  text-align: center;
  color: #ccc;
  margin-bottom: 4rem;
}

/* Hero Section */
.hero-section {
  height: 80vh;
  background-image: url('https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0 15%;
  text-align: left;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-content {
  max-width: 45%;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 1rem;
  margin-bottom: 2.5rem;
  max-width: 550px;
  line-height: 1.8;
  color: #ccc;
}

.hero-button {
  background-color: #c9302c;
  color: white;
  padding: 10px 25px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-button:hover {
  background-color: #d9534f;
}

/* 3D Isometric Section */
.isometric-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  padding: 6rem 15%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.isometric-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* Top Badge */
.top-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

/* Section Subtitle */
.section-subtitle {
  color: #b0b0b0;
  font-size: 16px;
  line-height: 1.6;
  margin: 1.5rem auto 3rem;
  max-width: 600px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 4rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 25px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  font-size: 16px;
}

.isometric-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  position: relative;
}

.isometric-model {
  position: relative;
  width: 800px;
  height: 600px;
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Platform Image Container */
.platform-image-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 300px;
  z-index: 5;
}

.platform-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Connection Lines */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Floating Data Cards */
.data-card {
  position: absolute;
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  max-width: 200px;
  z-index: 10;
  animation: float 4s ease-in-out infinite;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.card-1 {
  top: 15%;
  left: 5%;
  animation-delay: 0s;
}

.card-2 {
  top: 10%;
  right: 8%;
  animation-delay: 1s;
}

.card-3 {
  bottom: 25%;
  left: 3%;
  animation-delay: 2s;
}

.card-4 {
  bottom: 20%;
  right: 5%;
  animation-delay: 1.5s;
}

.card-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 12px;
  font-weight: bold;
}

.card-icon.red {
  background: #e74c3c;
}

.card-icon.blue {
  background: #3498db;
}

.card-icon.green {
  background: #27ae60;
}

.card-icon.orange {
  background: #f39c12;
}

.card-header {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
  padding: 2px 0;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Text Section */
.text-section {
  background-color: #f8f9fa;
  padding: 6rem 15%;
  text-align: center;
}

.text-content {
  max-width: 800px;
  margin: 0 auto;
}

.main-text {
  font-size: 1.8rem;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
}

/* Monitoring Interface Section */
.monitoring-section {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  padding: 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.wave-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23ffffff'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23ffffff'%3E%3C/path%3E%3C/svg%3E") no-repeat bottom;
  background-size: cover;
  opacity: 0.1;
}

.monitoring-content {
  position: relative;
  z-index: 2;
  padding: 6rem 15%;
}

.monitoring-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 4rem 0;
}

.laptop-frame {
  position: relative;
  width: 700px;
  height: 450px;
  perspective: 1000px;
}

.laptop-screen {
  width: 600px;
  height: 380px;
  background: #1a1a1a;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  border: 8px solid #2c3e50;
  border-bottom: none;
  position: relative;
  transform: rotateX(5deg);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.laptop-base {
  width: 620px;
  height: 20px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-radius: 0 0 20px 20px;
  position: absolute;
  top: 380px;
  left: -10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.laptop-base::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 2px;
}

.monitoring-interface {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1a1a1a 0%, #2c3e50 100%);
}

.interface-header {
  color: #3498db;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.interface-content {
  flex: 1;
  display: flex;
  gap: 20px;
}

.control-room-view {
  flex: 2;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #34495e;
}

.control-room-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.9) contrast(1.2) saturate(1.1);
}

.monitoring-panels {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.panel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(52, 152, 219, 0.3);
  transition: all 0.3s ease;
}

.panel-item:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: translateX(2px);
}

.panel-label {
  color: #ecf0f1;
  font-size: 13px;
  font-weight: 500;
}

.panel-status {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #7f8c8d;
  position: relative;
}

.panel-status.active {
  background: #27ae60;
  box-shadow: 0 0 10px #27ae60;
}

.panel-status.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: #ffffff;
  border-radius: 50%;
}

.panel-status.warning {
  background: #f39c12;
  box-shadow: 0 0 10px #f39c12;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.monitoring-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 4rem;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.feature-item {
  text-align: center;
  color: #ffffff;
}

.feature-item h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.feature-item p {
  font-size: 0.9rem;
  color: #cbd5e1;
  line-height: 1.6;
}

/* Application Scenarios Section */
.app-scenarios-section {
  background-color: #1a1a1a;
  padding: 6rem 15%;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.scenario-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  text-align: left;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-5px);
}

.scenario-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 1.5rem;
}

.scenario-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.scenario-card p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}


/* 响应式设计 - 保持原有布局，只在小屏幕时适配 */

/* 平板横屏 (768px - 1024px) */
@media (max-width: 1024px) {
  .hero-content {
    max-width: 60%;
  }

  .isometric-model {
    width: 600px;
    height: 450px;
  }

  .platform-image-container {
    width: 300px;
    height: 225px;
  }

  .laptop-frame {
    width: 500px;
    height: 350px;
  }

  .laptop-screen {
    width: 480px;
    height: 300px;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .monitoring-features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }
}

/* 平板竖屏 (768px以下) */
@media (max-width: 768px) {
  .isometric-section,
  .text-section,
  .app-scenarios-section {
    padding: 4rem 8%;
  }

  .monitoring-content {
    padding: 4rem 8%;
  }

  .hero-section {
    padding: 0 8%;
  }

  .hero-content {
    max-width: 80%;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.8rem;
  }

  .section-subtitle {
    font-size: 14px;
  }

  .main-text {
    font-size: 1.4rem;
  }

  .isometric-model {
    width: 500px;
    height: 400px;
  }

  .platform-image-container {
    width: 250px;
    height: 188px;
  }

  .laptop-frame {
    width: 400px;
    height: 280px;
  }

  .laptop-screen {
    width: 380px;
    height: 240px;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .interface-content {
    flex-direction: column;
    gap: 10px;
  }

  .control-room-view {
    height: 120px;
  }

  .monitoring-features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 3rem;
  }

  .action-btn {
    width: 200px;
    justify-content: center;
  }
}

/* 手机设备 (480px以下) */
@media (max-width: 480px) {
  .isometric-section,
  .text-section,
  .app-scenarios-section {
    padding: 3rem 5%;
  }

  .monitoring-content {
    padding: 3rem 5%;
  }

  .hero-section {
    padding: 0 5%;
  }

  .hero-content {
    max-width: 95%;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-subtitle {
    font-size: 0.8rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.5rem;
  }

  .section-subtitle {
    font-size: 13px;
    margin: 1rem auto 2rem;
  }

  .main-text {
    font-size: 1.2rem;
  }

  .isometric-model {
    width: 350px;
    height: 300px;
  }

  .platform-image-container {
    width: 200px;
    height: 150px;
  }

  .data-card {
    position: static;
    margin: 0.5rem auto;
    display: block;
    width: 90%;
    animation: none;
    max-width: none;
  }

  .isometric-container {
    flex-direction: column;
    gap: 2rem;
  }

  .connection-lines {
    display: none;
  }

  .laptop-frame {
    width: 320px;
    height: 220px;
  }

  .laptop-screen {
    width: 300px;
    height: 180px;
  }

  .interface-content {
    flex-direction: column;
    gap: 8px;
  }

  .control-room-view {
    height: 80px;
  }

  .monitoring-panels {
    flex-direction: column;
    gap: 5px;
    padding: 8px;
  }

  .panel-item {
    padding: 6px 8px;
  }

  .panel-label {
    font-size: 10px;
  }

  .scenario-card-image {
    height: 150px;
  }

  .monitoring-features {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 2rem;
  }

  .feature-item h4 {
    font-size: 1rem;
  }

  .feature-item p {
    font-size: 0.8rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 2rem;
  }

  .action-btn {
    width: 180px;
    justify-content: center;
    padding: 10px 20px;
    font-size: 13px;
  }

  .top-badge {
    font-size: 12px;
    padding: 6px 16px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 320px) {
  .isometric-section,
  .text-section,
  .app-scenarios-section {
    padding: 2rem 3%;
  }

  .monitoring-content {
    padding: 2rem 3%;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.3rem;
  }

  .main-text {
    font-size: 1rem;
  }

  .isometric-model {
    width: 280px;
    height: 200px;
  }

  .platform-3d {
    width: 140px;
    height: 140px;
  }

  .laptop-frame {
    width: 280px;
    height: 180px;
  }

  .laptop-screen {
    width: 260px;
    height: 150px;
  }

  .data-card {
    width: 95%;
    padding: 8px;
  }

  .card-header {
    font-size: 10px;
  }

  .data-item {
    font-size: 9px;
  }

  .human-icon {
    font-size: 16px;
  }
}
</style>
