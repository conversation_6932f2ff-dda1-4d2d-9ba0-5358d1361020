<template>
  <div class="power-monitoring-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          三维智能联动平台实现全球精准决策与毫秒级响应。
        </h1>
        <p class="hero-subtitle">
          构建变电站级三维孪生体,融合SCADA实时数据与设备
          历史档案,实现从主变压器到绝缘子的全生命周期健康管
          理,支持设备拆解透视与故障回溯
        </p>
        <button class="hero-button">See more</button>
      </div>
    </section>

    <!-- 3D Isometric Section -->
    <section class="isometric-section">
      <h2 class="section-title">覆盖配电的设计、建造、运营、维护的全场景应用</h2>
      <div class="isometric-container">
        <div class="isometric-model">
          <!-- 3D Power Station Model -->
          <div class="power-station-3d">
            <div class="station-base"></div>
            <div class="station-building"></div>
            <div class="equipment-area">
              <div class="transformer-unit"></div>
              <div class="control-panel"></div>
            </div>
            <!-- Human figures for scale -->
            <div class="human-figure figure-1"></div>
            <div class="human-figure figure-2"></div>
          </div>

          <!-- Floating Data Cards -->
          <div class="data-card card-1">
            <div class="card-header">实时监测</div>
            <div class="card-content">
              <div class="data-item">
                <span>电压</span>
                <span class="value">220V</span>
              </div>
              <div class="data-item">
                <span>电流</span>
                <span class="value">15A</span>
              </div>
            </div>
          </div>

          <div class="data-card card-2">
            <div class="card-header">三维可视化</div>
            <div class="card-content">
              <div class="data-item">
                <span>设备状态</span>
                <span class="value status-normal">正常</span>
              </div>
            </div>
          </div>

          <div class="data-card card-3">
            <div class="card-header">AI智能分析</div>
            <div class="card-content">
              <div class="data-item">
                <span>预警等级</span>
                <span class="value">低风险</span>
              </div>
            </div>
          </div>

          <div class="data-card card-4">
            <div class="card-header">故障诊断</div>
            <div class="card-content">
              <div class="data-item">
                <span>系统健康度</span>
                <span class="value">98%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Text Section -->
    <section class="text-section">
      <div class="text-content">
        <p class="main-text">在国际局势复杂多变与低碳发展双重驱动下,依托自主可控技术保障电力系统稳定高效运行</p>
      </div>
    </section>

    <!-- Monitoring Interface Section -->
    <section class="monitoring-section">
      <h2 class="section-title">全面打通连接、监测、治理的全数字化流程,形成智能配电闭环价值落地</h2>
      <div class="monitoring-display">
        <div class="laptop-frame">
          <div class="laptop-screen">
            <div class="monitoring-interface">
              <div class="interface-header">电力监控系统</div>
              <div class="interface-content">
                <div class="control-room-view">
                  <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=800&h=400&fit=crop" alt="Control Room" class="control-room-image">
                </div>
                <div class="monitoring-panels">
                  <div class="panel-item">
                    <span class="panel-label">主变压器</span>
                    <span class="panel-status active"></span>
                  </div>
                  <div class="panel-item">
                    <span class="panel-label">配电柜</span>
                    <span class="panel-status active"></span>
                  </div>
                  <div class="panel-item">
                    <span class="panel-label">监控系统</span>
                    <span class="panel-status warning"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios Section -->
    <section class="app-scenarios-section">
      <h2 class="section-title-dark">电力监控系统应用场景</h2>
      <p class="section-subtitle-dark">三大类电力用户,N种安定设备,N种解决方案</p>
      <div class="scenarios-grid">
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400&h=250&fit=crop" alt="老旧电网" class="scenario-card-image">
          <div class="card-content">
            <h3>老旧电网</h3>
            <p>设备智能化改造，提高效率50%</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=250&fit=crop" alt="城市变电站" class="scenario-card-image">
          <div class="card-content">
            <h3>城市变电站</h3>
            <p>三维孪生可视化，辅助决策</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=250&fit=crop" alt="新建电厂" class="scenario-card-image">
          <div class="card-content">
            <h3>新建电厂</h3>
            <p>数字化生产平台，运维成本降低30%</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Page logic for Power Monitoring Solution
</script>

<style scoped>
:root {
  /* 响应式缩放变量 */
  --scale-factor: 1;
  --section-padding: clamp(2rem, 8vw, 6rem);
  --container-padding: clamp(5%, 15vw, 15%);
  --title-size: clamp(1.5rem, 4vw, 2.2rem);
  --subtitle-size: clamp(0.9rem, 2.5vw, 1.1rem);
  --text-size: clamp(0.8rem, 2vw, 1rem);
  --hero-title-size: clamp(1.8rem, 5vw, 2.8rem);
  --card-size: clamp(80px, 15vw, 120px);
  --model-width: clamp(250px, 50vw, 600px);
  --model-height: clamp(200px, 35vw, 400px);
}

.power-monitoring-page {
  background-color: #000;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  overflow-x: hidden;
  width: 100%;
}

.section-title {
  font-size: var(--title-size);
  text-align: center;
  margin-bottom: clamp(2rem, 6vw, 4rem);
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
  padding: 0 1rem;
}

.section-title-dark {
  font-size: var(--title-size);
  text-align: center;
  margin-bottom: clamp(1rem, 4vw, 2rem);
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
  padding: 0 1rem;
}

.section-subtitle-dark {
  font-size: var(--subtitle-size);
  text-align: center;
  color: #ccc;
  margin-bottom: clamp(2rem, 6vw, 4rem);
  padding: 0 1rem;
}

/* Hero Section */
.hero-section {
  min-height: clamp(60vh, 80vh, 90vh);
  background-image: url('https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: var(--section-padding) var(--container-padding);
  text-align: left;
  position: relative;
  width: 100%;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-content {
  max-width: min(90%, 600px);
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-title {
  font-size: var(--hero-title-size);
  font-weight: 500;
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
  line-height: 1.3;
  word-wrap: break-word;
}

.hero-subtitle {
  font-size: var(--text-size);
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  max-width: 100%;
  line-height: 1.8;
  color: #ccc;
  word-wrap: break-word;
}

.hero-button {
  background-color: #c9302c;
  color: white;
  padding: clamp(8px, 2vw, 12px) clamp(20px, 4vw, 25px);
  border: none;
  border-radius: 4px;
  font-size: var(--text-size);
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px; /* 确保触摸友好 */
  min-width: 100px;
}

.hero-button:hover {
  background-color: #d9534f;
  transform: translateY(-2px);
}

.hero-button:active {
  transform: translateY(0);
}

/* 3D Isometric Section */
.isometric-section {
  background-color: #1a1a1a;
  padding: var(--section-padding) var(--container-padding);
  position: relative;
  width: 100%;
  overflow: hidden;
}

.isometric-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: clamp(400px, 60vh, 600px);
  position: relative;
  width: 100%;
}

.isometric-model {
  position: relative;
  width: var(--model-width);
  height: var(--model-height);
  transform-style: preserve-3d;
  perspective: clamp(800px, 100vw, 1200px);
  max-width: 90vw;
}

/* 3D Power Station Model */
.power-station-3d {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(15deg) rotateY(-15deg);
  width: clamp(200px, 40vw, 300px);
  height: clamp(130px, 25vw, 200px);
}

.station-base {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: clamp(4px, 1vw, 8px);
  position: relative;
  box-shadow: 0 clamp(5px, 2vw, 10px) clamp(15px, 5vw, 30px) rgba(0, 0, 0, 0.3);
}

.station-building {
  position: absolute;
  top: clamp(-25px, -8vw, -40px);
  left: clamp(30px, 15%, 50px);
  width: clamp(50px, 25%, 80px);
  height: clamp(40px, 20%, 60px);
  background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
  border-radius: clamp(2px, 0.5vw, 4px);
  box-shadow: 0 clamp(3px, 1vw, 5px) clamp(8px, 3vw, 15px) rgba(0, 0, 0, 0.2);
}

.equipment-area {
  position: absolute;
  top: clamp(-15px, -5vw, -20px);
  right: clamp(25px, 12%, 40px);
  display: flex;
  gap: clamp(10px, 3vw, 20px);
}

.transformer-unit {
  width: clamp(20px, 6vw, 30px);
  height: clamp(25px, 8vw, 40px);
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: clamp(2px, 0.5vw, 4px);
  box-shadow: 0 clamp(2px, 1vw, 3px) clamp(5px, 2vw, 10px) rgba(0, 0, 0, 0.2);
}

.control-panel {
  width: clamp(15px, 5vw, 25px);
  height: clamp(20px, 7vw, 35px);
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-radius: clamp(2px, 0.5vw, 3px);
  box-shadow: 0 clamp(2px, 1vw, 3px) clamp(5px, 2vw, 10px) rgba(0, 0, 0, 0.2);
}

/* Human Figures */
.human-figure {
  position: absolute;
  width: clamp(5px, 2vw, 8px);
  height: clamp(12px, 4vw, 20px);
  background: #34495e;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
}

.figure-1 {
  bottom: 0;
  left: clamp(60px, 30%, 100px);
}

.figure-2 {
  bottom: 0;
  right: clamp(50px, 25%, 80px);
}

/* Floating Data Cards */
.data-card {
  position: absolute;
  background: #ffffff;
  border-radius: clamp(4px, 1vw, 8px);
  padding: clamp(8px, 2vw, 15px);
  box-shadow: 0 clamp(4px, 2vw, 8px) clamp(12px, 4vw, 25px) rgba(0, 0, 0, 0.15);
  min-width: var(--card-size);
  max-width: clamp(100px, 25vw, 180px);
  z-index: 10;
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.card-1 {
  top: clamp(15%, 20%, 25%);
  left: clamp(2%, 10%, 15%);
  background: #fff5f5;
  border-left: clamp(2px, 0.5vw, 4px) solid #e74c3c;
  animation-delay: 0s;
}

.card-2 {
  top: clamp(10%, 15%, 20%);
  right: clamp(2%, 15%, 20%);
  background: #f0f8ff;
  border-left: clamp(2px, 0.5vw, 4px) solid #3498db;
  animation-delay: 1s;
}

.card-3 {
  bottom: clamp(20%, 25%, 30%);
  left: clamp(1%, 5%, 10%);
  background: #f0fff0;
  border-left: clamp(2px, 0.5vw, 4px) solid #27ae60;
  animation-delay: 2s;
}

.card-4 {
  bottom: clamp(25%, 30%, 35%);
  right: clamp(2%, 10%, 15%);
  background: #fffaf0;
  border-left: clamp(2px, 0.5vw, 4px) solid #f39c12;
  animation-delay: 1.5s;
}

.card-header {
  font-size: clamp(10px, 2.5vw, 12px);
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: clamp(4px, 1vw, 8px);
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: clamp(3px, 0.5vw, 5px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: clamp(3px, 1vw, 5px);
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: clamp(9px, 2vw, 11px);
  color: #34495e;
  gap: clamp(4px, 1vw, 8px);
}

.data-item span:first-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.value {
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
}

.status-normal {
  color: #27ae60;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(clamp(-5px, -2vw, -10px));
  }
}

/* Text Section */
.text-section {
  background-color: #f8f9fa;
  padding: var(--section-padding) var(--container-padding);
  text-align: center;
  width: 100%;
}

.text-content {
  max-width: min(90%, 800px);
  margin: 0 auto;
  padding: 0 1rem;
}

.main-text {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
  word-wrap: break-word;
}

/* Monitoring Interface Section */
.monitoring-section {
  background-color: #1a1a1a;
  padding: var(--section-padding) var(--container-padding);
  text-align: center;
  width: 100%;
}

.monitoring-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: clamp(2rem, 6vw, 4rem);
  width: 100%;
}

.laptop-frame {
  position: relative;
  width: min(90vw, var(--model-width));
  height: clamp(250px, 50vw, 400px);
  background: #2c3e50;
  border-radius: clamp(8px, 2vw, 12px);
  padding: clamp(10px, 3vw, 20px);
  box-shadow: 0 clamp(10px, 4vw, 20px) clamp(20px, 6vw, 40px) rgba(0, 0, 0, 0.3);
  max-width: 100%;
}

.laptop-screen {
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  border-radius: clamp(4px, 1vw, 8px);
  overflow: hidden;
  border: clamp(1px, 0.3vw, 2px) solid #34495e;
}

.monitoring-interface {
  width: 100%;
  height: 100%;
  padding: clamp(8px, 2vw, 15px);
  display: flex;
  flex-direction: column;
}

.interface-header {
  color: #3498db;
  font-size: clamp(12px, 3vw, 16px);
  font-weight: 600;
  text-align: center;
  margin-bottom: clamp(8px, 2vw, 15px);
  border-bottom: 1px solid #3498db;
  padding-bottom: clamp(4px, 1vw, 8px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.interface-content {
  flex: 1;
  display: flex;
  gap: clamp(8px, 2vw, 15px);
  min-height: 0;
}

.control-room-view {
  flex: 2;
  border-radius: clamp(3px, 1vw, 6px);
  overflow: hidden;
  min-width: 0;
}

.control-room-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.1);
}

.monitoring-panels {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: clamp(5px, 1.5vw, 10px);
  padding: clamp(5px, 1.5vw, 10px);
  min-width: 0;
}

.panel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(4px, 1vw, 8px) clamp(6px, 1.5vw, 12px);
  background: rgba(52, 152, 219, 0.1);
  border-radius: clamp(2px, 0.5vw, 4px);
  border: 1px solid rgba(52, 152, 219, 0.3);
  min-height: clamp(24px, 5vw, 32px);
}

.panel-label {
  color: #ecf0f1;
  font-size: clamp(10px, 2.5vw, 12px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.panel-status {
  width: clamp(6px, 1.5vw, 8px);
  height: clamp(6px, 1.5vw, 8px);
  border-radius: 50%;
  background: #7f8c8d;
  flex-shrink: 0;
  margin-left: clamp(4px, 1vw, 8px);
}

.panel-status.active {
  background: #27ae60;
  box-shadow: 0 0 clamp(4px, 1vw, 8px) #27ae60;
}

.panel-status.warning {
  background: #f39c12;
  box-shadow: 0 0 clamp(4px, 1vw, 8px) #f39c12;
}

/* Application Scenarios Section */
.app-scenarios-section {
  background-color: #1a1a1a;
  padding: var(--section-padding) var(--container-padding);
  width: 100%;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  max-width: 1200px;
  margin: 0 auto;
}

.scenario-card {
  background-color: #fff;
  border-radius: clamp(6px, 1.5vw, 8px);
  overflow: hidden;
  text-align: left;
  box-shadow: 0 clamp(2px, 1vw, 4px) clamp(8px, 3vw, 15px) rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.scenario-card:hover {
  transform: translateY(clamp(-3px, -1vw, -5px));
  box-shadow: 0 clamp(4px, 2vw, 8px) clamp(15px, 5vw, 25px) rgba(0,0,0,0.15);
}

.scenario-card-image {
  width: 100%;
  height: clamp(150px, 30vw, 200px);
  object-fit: cover;
  transition: transform 0.3s ease;
}

.scenario-card:hover .scenario-card-image {
  transform: scale(1.05);
}

.card-content {
  padding: clamp(1rem, 3vw, 1.5rem);
}

.scenario-card h3 {
  font-size: clamp(1.1rem, 3vw, 1.3rem);
  font-weight: 600;
  margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
  color: #2c3e50;
  word-wrap: break-word;
}

.scenario-card p {
  font-size: clamp(0.8rem, 2.5vw, 0.9rem);
  color: #666;
  line-height: 1.5;
  word-wrap: break-word;
}


/* 超大屏幕优化 (1920px+) */
@media (min-width: 1920px) {
  :root {
    --scale-factor: 1.2;
    --section-padding: 8rem;
    --container-padding: 20%;
  }

  .hero-section {
    background-attachment: fixed;
  }

  .isometric-model {
    width: 700px;
    height: 500px;
  }

  .scenarios-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1400px;
  }
}

/* 大桌面屏幕 (1440px - 1920px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  :root {
    --scale-factor: 1.1;
    --section-padding: 7rem;
    --container-padding: 18%;
  }
}

/* 中等桌面屏幕 (1200px - 1440px) */
@media (min-width: 1200px) and (max-width: 1439px) {
  :root {
    --scale-factor: 1;
    --section-padding: 6rem;
    --container-padding: 15%;
  }
}

/* 小桌面/大平板 (1024px - 1200px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  :root {
    --scale-factor: 0.9;
    --section-padding: 5rem;
    --container-padding: 12%;
  }

  .hero-content {
    max-width: 60%;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .interface-content {
    flex-direction: column;
    gap: clamp(10px, 2vw, 15px);
  }

  .control-room-view {
    flex: none;
    height: 60%;
  }

  .monitoring-panels {
    flex: none;
    height: 40%;
    flex-direction: row;
    flex-wrap: wrap;
  }
}

/* 平板横屏 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --scale-factor: 0.8;
    --section-padding: 4rem;
    --container-padding: 8%;
  }

  .hero-section {
    background-attachment: scroll;
  }

  .hero-content {
    max-width: 70%;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .data-card {
    position: static;
    margin: 1rem;
    display: inline-block;
    animation: none;
  }

  .isometric-container {
    flex-direction: column;
    gap: 2rem;
  }

  .isometric-model::after {
    content: '';
    display: block;
    width: 100%;
    height: 2rem;
  }
}

/* 平板竖屏/大手机 (600px - 768px) */
@media (min-width: 600px) and (max-width: 767px) {
  :root {
    --scale-factor: 0.7;
    --section-padding: 3rem;
    --container-padding: 6%;
  }

  .hero-content {
    max-width: 85%;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
  }

  .interface-content {
    flex-direction: column;
  }

  .control-room-view {
    height: 200px;
  }

  .monitoring-panels {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .panel-item {
    flex: 0 1 45%;
    margin-bottom: 0.5rem;
  }
}

/* 大手机 (480px - 600px) */
@media (min-width: 480px) and (max-width: 599px) {
  :root {
    --scale-factor: 0.6;
    --section-padding: 2.5rem;
    --container-padding: 4%;
  }

  .hero-content {
    max-width: 90%;
  }

  .data-card {
    position: static;
    margin: 0.5rem;
    display: block;
    width: calc(50% - 1rem);
    float: left;
    animation: none;
  }

  .isometric-container {
    flex-direction: column;
    align-items: center;
  }

  .isometric-model {
    margin-bottom: 2rem;
  }

  .isometric-model::after {
    content: '';
    display: block;
    clear: both;
    width: 100%;
    padding-top: 1rem;
  }

  .interface-content {
    flex-direction: column;
    gap: 1rem;
  }

  .control-room-view {
    height: 150px;
  }

  .monitoring-panels {
    flex-direction: column;
    gap: 0.5rem;
  }

  .panel-item {
    min-height: 36px;
  }
}

/* 标准手机 (375px - 480px) */
@media (min-width: 375px) and (max-width: 479px) {
  :root {
    --scale-factor: 0.5;
    --section-padding: 2rem;
    --container-padding: 3%;
  }

  .hero-content {
    max-width: 95%;
  }

  .data-card {
    position: static;
    margin: 0.5rem auto;
    display: block;
    width: 90%;
    float: none;
    animation: none;
  }

  .isometric-container {
    flex-direction: column;
    padding: 1rem;
  }

  .power-station-3d {
    transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg);
  }

  .interface-content {
    flex-direction: column;
    gap: 0.5rem;
  }

  .control-room-view {
    height: 120px;
  }

  .monitoring-panels {
    flex-direction: column;
    padding: 0.5rem;
  }

  .panel-item {
    min-height: 32px;
    padding: 0.5rem;
  }

  .panel-label {
    font-size: 11px;
  }
}

/* 小手机 (320px - 375px) */
@media (min-width: 320px) and (max-width: 374px) {
  :root {
    --scale-factor: 0.45;
    --section-padding: 1.5rem;
    --container-padding: 2%;
    --hero-title-size: 1.5rem;
    --title-size: 1.3rem;
    --text-size: 0.8rem;
  }

  .hero-content {
    max-width: 98%;
  }

  .hero-button {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    display: block;
  }

  .data-card {
    position: static;
    margin: 0.3rem auto;
    display: block;
    width: 95%;
    animation: none;
    padding: 0.5rem;
  }

  .card-header {
    font-size: 11px;
  }

  .data-item {
    font-size: 10px;
  }

  .isometric-container {
    padding: 0.5rem;
  }

  .power-station-3d {
    transform: translate(-50%, -50%);
  }

  .laptop-frame {
    padding: 0.5rem;
  }

  .interface-header {
    font-size: 12px;
  }

  .control-room-view {
    height: 100px;
  }

  .panel-item {
    min-height: 28px;
    padding: 0.3rem 0.5rem;
  }

  .panel-label {
    font-size: 10px;
  }

  .scenario-card-image {
    height: 120px;
  }
}

/* 超小屏幕 (< 320px) */
@media (max-width: 319px) {
  :root {
    --scale-factor: 0.4;
    --section-padding: 1rem;
    --container-padding: 1%;
    --hero-title-size: 1.3rem;
    --title-size: 1.1rem;
    --text-size: 0.75rem;
  }

  .power-monitoring-page {
    min-width: 280px;
  }

  .hero-content {
    max-width: 100%;
  }

  .hero-button {
    width: 100%;
    font-size: 0.9rem;
    padding: 0.8rem;
  }

  .data-card {
    position: static;
    margin: 0.2rem auto;
    display: block;
    width: 98%;
    animation: none;
    padding: 0.4rem;
  }

  .isometric-model {
    width: 100%;
    height: auto;
    min-height: 200px;
  }

  .power-station-3d {
    position: relative;
    transform: none;
    top: auto;
    left: auto;
    margin: 1rem auto;
  }

  .laptop-frame {
    width: 100%;
    height: 200px;
    padding: 0.3rem;
  }

  .interface-content {
    flex-direction: column;
    gap: 0.3rem;
  }

  .control-room-view {
    height: 80px;
  }

  .monitoring-panels {
    flex-direction: column;
    gap: 0.2rem;
    padding: 0.3rem;
  }

  .panel-item {
    min-height: 24px;
    padding: 0.2rem 0.4rem;
  }

  .panel-label {
    font-size: 9px;
  }

  .scenario-card-image {
    height: 100px;
  }

  .card-content {
    padding: 0.8rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hero-button,
  .scenario-card,
  .data-card {
    transition: none;
  }

  .hero-button:hover,
  .scenario-card:hover {
    transform: none;
  }

  .hero-button:active {
    transform: scale(0.98);
  }

  .scenario-card:active {
    transform: scale(0.98);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .data-card {
    border: 2px solid #000;
  }

  .panel-status.active {
    border: 2px solid #000;
  }

  .panel-status.warning {
    border: 2px solid #000;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .data-card,
  .hero-button,
  .scenario-card {
    animation: none;
    transition: none;
  }

  .hero-section {
    background-attachment: scroll;
  }
}
</style>
