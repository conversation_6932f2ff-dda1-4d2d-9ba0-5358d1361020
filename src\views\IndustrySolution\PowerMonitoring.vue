<template>
  <div class="power-monitoring-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          三维智能联动平台实现全球精准决策与毫秒级响应。
        </h1>
        <p class="hero-subtitle">
          构建变电站级三维孪生体,融合SCADA实时数据与设备
          历史档案,实现从主变压器到绝缘子的全生命周期健康管
          理,支持设备拆解透视与故障回溯
        </p>
        <button class="hero-button">See more</button>
      </div>
    </section>

    <!-- 3D Isometric Section -->
    <section class="isometric-section">
      <h2 class="section-title">覆盖配电的设计、建造、运营、维护的全场景应用</h2>
      <div class="isometric-container">
        <div class="isometric-model">
          <!-- 3D Power Station Model -->
          <div class="power-station-3d">
            <div class="station-base"></div>
            <div class="station-building"></div>
            <div class="equipment-area">
              <div class="transformer-unit"></div>
              <div class="control-panel"></div>
            </div>
            <!-- Human figures for scale -->
            <div class="human-figure figure-1"></div>
            <div class="human-figure figure-2"></div>
          </div>

          <!-- Floating Data Cards -->
          <div class="data-card card-1">
            <div class="card-header">实时监测</div>
            <div class="card-content">
              <div class="data-item">
                <span>电压</span>
                <span class="value">220V</span>
              </div>
              <div class="data-item">
                <span>电流</span>
                <span class="value">15A</span>
              </div>
            </div>
          </div>

          <div class="data-card card-2">
            <div class="card-header">三维可视化</div>
            <div class="card-content">
              <div class="data-item">
                <span>设备状态</span>
                <span class="value status-normal">正常</span>
              </div>
            </div>
          </div>

          <div class="data-card card-3">
            <div class="card-header">AI智能分析</div>
            <div class="card-content">
              <div class="data-item">
                <span>预警等级</span>
                <span class="value">低风险</span>
              </div>
            </div>
          </div>

          <div class="data-card card-4">
            <div class="card-header">故障诊断</div>
            <div class="card-content">
              <div class="data-item">
                <span>系统健康度</span>
                <span class="value">98%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Text Section -->
    <section class="text-section">
      <div class="text-content">
        <p class="main-text">在国际局势复杂多变与低碳发展双重驱动下,依托自主可控技术保障电力系统稳定高效运行</p>
      </div>
    </section>

    <!-- Monitoring Interface Section -->
    <section class="monitoring-section">
      <h2 class="section-title">全面打通连接、监测、治理的全数字化流程,形成智能配电闭环价值落地</h2>
      <div class="monitoring-display">
        <div class="laptop-frame">
          <div class="laptop-screen">
            <div class="monitoring-interface">
              <div class="interface-header">电力监控系统</div>
              <div class="interface-content">
                <div class="control-room-view">
                  <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=800&h=400&fit=crop" alt="Control Room" class="control-room-image">
                </div>
                <div class="monitoring-panels">
                  <div class="panel-item">
                    <span class="panel-label">主变压器</span>
                    <span class="panel-status active"></span>
                  </div>
                  <div class="panel-item">
                    <span class="panel-label">配电柜</span>
                    <span class="panel-status active"></span>
                  </div>
                  <div class="panel-item">
                    <span class="panel-label">监控系统</span>
                    <span class="panel-status warning"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios Section -->
    <section class="app-scenarios-section">
      <h2 class="section-title-dark">电力监控系统应用场景</h2>
      <p class="section-subtitle-dark">三大类电力用户,N种安定设备,N种解决方案</p>
      <div class="scenarios-grid">
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400&h=250&fit=crop" alt="老旧电网" class="scenario-card-image">
          <div class="card-content">
            <h3>老旧电网</h3>
            <p>设备智能化改造，提高效率50%</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=250&fit=crop" alt="城市变电站" class="scenario-card-image">
          <div class="card-content">
            <h3>城市变电站</h3>
            <p>三维孪生可视化，辅助决策</p>
          </div>
        </div>
        <div class="scenario-card">
          <img src="https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=250&fit=crop" alt="新建电厂" class="scenario-card-image">
          <div class="card-content">
            <h3>新建电厂</h3>
            <p>数字化生产平台，运维成本降低30%</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Page logic for Power Monitoring Solution
</script>

<style scoped>
.power-monitoring-page {
  background-color: #000;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.section-title {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-title-dark {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-subtitle-dark {
  font-size: 1.1rem;
  text-align: center;
  color: #ccc;
  margin-bottom: 4rem;
}

/* Hero Section */
.hero-section {
  height: 80vh;
  background-image: url('https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0 15%;
  text-align: left;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-content {
  max-width: 45%;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 1rem;
  margin-bottom: 2.5rem;
  max-width: 550px;
  line-height: 1.8;
  color: #ccc;
}

.hero-button {
  background-color: #c9302c;
  color: white;
  padding: 10px 25px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-button:hover {
  background-color: #d9534f;
}

/* 3D Isometric Section */
.isometric-section {
  background-color: #1a1a1a;
  padding: 6rem 15%;
  position: relative;
}

.isometric-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  position: relative;
}

.isometric-model {
  position: relative;
  width: 600px;
  height: 400px;
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 3D Power Station Model */
.power-station-3d {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(15deg) rotateY(-15deg);
  width: 300px;
  height: 200px;
}

.station-base {
  width: 300px;
  height: 200px;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 8px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.station-building {
  position: absolute;
  top: -40px;
  left: 50px;
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.equipment-area {
  position: absolute;
  top: -20px;
  right: 40px;
  display: flex;
  gap: 20px;
}

.transformer-unit {
  width: 30px;
  height: 40px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.control-panel {
  width: 25px;
  height: 35px;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-radius: 3px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Human Figures */
.human-figure {
  position: absolute;
  width: 8px;
  height: 20px;
  background: #34495e;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
}

.figure-1 {
  bottom: 0;
  left: 100px;
}

.figure-2 {
  bottom: 0;
  right: 80px;
}

/* Floating Data Cards */
.data-card {
  position: absolute;
  background: #ffffff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 10;
  animation: float 3s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  background: #fff5f5;
  border-left: 4px solid #e74c3c;
  animation-delay: 0s;
}

.card-2 {
  top: 15%;
  right: 15%;
  background: #f0f8ff;
  border-left: 4px solid #3498db;
  animation-delay: 1s;
}

.card-3 {
  bottom: 25%;
  left: 5%;
  background: #f0fff0;
  border-left: 4px solid #27ae60;
  animation-delay: 2s;
}

.card-4 {
  bottom: 30%;
  right: 10%;
  background: #fffaf0;
  border-left: 4px solid #f39c12;
  animation-delay: 1.5s;
}

.card-header {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: 5px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #34495e;
}

.value {
  font-weight: 600;
  color: #2c3e50;
}

.status-normal {
  color: #27ae60;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Text Section */
.text-section {
  background-color: #f8f9fa;
  padding: 6rem 15%;
  text-align: center;
}

.text-content {
  max-width: 800px;
  margin: 0 auto;
}

.main-text {
  font-size: 1.8rem;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 400;
}

/* Monitoring Interface Section */
.monitoring-section {
  background-color: #1a1a1a;
  padding: 6rem 15%;
  text-align: center;
}

.monitoring-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 4rem;
}

.laptop-frame {
  position: relative;
  width: 600px;
  height: 400px;
  background: #2c3e50;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.laptop-screen {
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #34495e;
}

.monitoring-interface {
  width: 100%;
  height: 100%;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.interface-header {
  color: #3498db;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #3498db;
  padding-bottom: 8px;
}

.interface-content {
  flex: 1;
  display: flex;
  gap: 15px;
}

.control-room-view {
  flex: 2;
  border-radius: 6px;
  overflow: hidden;
}

.control-room-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.1);
}

.monitoring-panels {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
}

.panel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.panel-label {
  color: #ecf0f1;
  font-size: 12px;
}

.panel-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #7f8c8d;
}

.panel-status.active {
  background: #27ae60;
  box-shadow: 0 0 8px #27ae60;
}

.panel-status.warning {
  background: #f39c12;
  box-shadow: 0 0 8px #f39c12;
}

/* Application Scenarios Section */
.app-scenarios-section {
  background-color: #1a1a1a;
  padding: 6rem 15%;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.scenario-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  text-align: left;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-5px);
}

.scenario-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 1.5rem;
}

.scenario-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.scenario-card p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}


/* 响应式设计 - 保持原有布局，只在小屏幕时适配 */

/* 平板横屏 (768px - 1024px) */
@media (max-width: 1024px) {
  .hero-content {
    max-width: 60%;
  }

  .isometric-model {
    width: 500px;
    height: 350px;
  }

  .laptop-frame {
    width: 500px;
    height: 350px;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 平板竖屏 (768px以下) */
@media (max-width: 768px) {
  .isometric-section,
  .text-section,
  .monitoring-section,
  .app-scenarios-section {
    padding: 4rem 8%;
  }

  .hero-section {
    padding: 0 8%;
  }

  .hero-content {
    max-width: 80%;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.8rem;
  }

  .main-text {
    font-size: 1.4rem;
  }

  .isometric-model {
    width: 400px;
    height: 300px;
  }

  .power-station-3d {
    width: 250px;
    height: 150px;
  }

  .laptop-frame {
    width: 400px;
    height: 280px;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .interface-content {
    flex-direction: column;
    gap: 10px;
  }

  .control-room-view {
    height: 150px;
  }
}

/* 手机设备 (480px以下) */
@media (max-width: 480px) {
  .isometric-section,
  .text-section,
  .monitoring-section,
  .app-scenarios-section {
    padding: 3rem 5%;
  }

  .hero-section {
    padding: 0 5%;
  }

  .hero-content {
    max-width: 95%;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-subtitle {
    font-size: 0.8rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.5rem;
  }

  .main-text {
    font-size: 1.2rem;
  }

  .isometric-model {
    width: 320px;
    height: 250px;
  }

  .power-station-3d {
    width: 200px;
    height: 120px;
  }

  .data-card {
    position: static;
    margin: 0.5rem auto;
    display: block;
    width: 90%;
    animation: none;
  }

  .isometric-container {
    flex-direction: column;
    gap: 2rem;
  }

  .laptop-frame {
    width: 320px;
    height: 220px;
  }

  .interface-content {
    flex-direction: column;
    gap: 8px;
  }

  .control-room-view {
    height: 100px;
  }

  .monitoring-panels {
    flex-direction: column;
    gap: 5px;
    padding: 5px;
  }

  .panel-item {
    padding: 6px 8px;
  }

  .panel-label {
    font-size: 10px;
  }

  .scenario-card-image {
    height: 150px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 320px) {
  .isometric-section,
  .text-section,
  .monitoring-section,
  .app-scenarios-section {
    padding: 2rem 3%;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .section-title,
  .section-title-dark {
    font-size: 1.3rem;
  }

  .main-text {
    font-size: 1rem;
  }

  .isometric-model {
    width: 280px;
    height: 200px;
  }

  .power-station-3d {
    width: 180px;
    height: 100px;
  }

  .laptop-frame {
    width: 280px;
    height: 180px;
  }

  .data-card {
    width: 95%;
    padding: 8px;
  }

  .card-header {
    font-size: 10px;
  }

  .data-item {
    font-size: 9px;
  }
}
</style>
